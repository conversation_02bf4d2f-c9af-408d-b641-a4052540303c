import { useEffect, useState } from 'react';

import * as DarkRead<PERSON> from 'darkreader';

interface DarkReaderConfig {
  brightness: number;
  contrast: number;
  sepia: number;
  grayscale?: number;
}

interface UseDarkReaderOptions {
  enabled?: boolean;
  config?: DarkReaderConfig;
  autoDetectSystemTheme?: boolean;
}

const DEFAULT_CONFIG: DarkReaderConfig = {
  brightness: 105,
  contrast: 95,
  sepia: 8,
  grayscale: 0,
};

// eslint-disable-next-line max-lines-per-function
export const useDarkReader = (options: UseDarkReaderOptions = {}) => {
  const {
    enabled = true,
    config = DEFAULT_CONFIG,
    autoDetectSystemTheme = false,
  } = options;

  const [isEnabled, setIsEnabled] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [currentConfig, setCurrentConfig] = useState(config);

  useEffect(() => {
    if (!enabled) {
      DarkReader.disable();
      setIsEnabled(false);
      setIsReady(true);
      return;
    }

    // Hide content initially to prevent flash
    document.body.style.visibility = 'hidden';

    const applyDarkReader = () => {
      if (autoDetectSystemTheme) {
        DarkReader.auto(currentConfig);
      } else {
        DarkReader.enable(currentConfig);
      }

      setIsEnabled(true);

      // Show content after a brief delay to ensure DarkReader has applied
      setTimeout(() => {
        document.body.style.visibility = 'visible';
        setIsReady(true);
      }, 100);
    };

    // Apply DarkReader immediately
    applyDarkReader();

    // Cleanup function
    return () => {
      DarkReader.disable();
      document.body.style.visibility = 'visible';
    };
  }, [enabled, currentConfig, autoDetectSystemTheme]);

  const enable = (newConfig?: DarkReaderConfig) => {
    const configToUse = newConfig ?? currentConfig;
    DarkReader.enable(configToUse);
    setCurrentConfig(configToUse);
    setIsEnabled(true);
  };

  const disable = () => {
    DarkReader.disable();
    setIsEnabled(false);
  };

  const toggle = () => {
    if (isEnabled) {
      disable();
    } else {
      enable();
    }
  };

  const updateConfig = (newConfig: Partial<DarkReaderConfig>) => {
    const updatedConfig = { ...currentConfig, ...newConfig };
    setCurrentConfig(updatedConfig);
    if (isEnabled) {
      DarkReader.enable(updatedConfig);
    }
  };

  const exportCSS = async (): Promise<string> => {
    return await DarkReader.exportGeneratedCSS();
  };

  return {
    isEnabled,
    isReady,
    config: currentConfig,
    enable,
    disable,
    toggle,
    updateConfig,
    exportCSS,
    isDarkReaderSupported: DarkReader !== undefined,
  };
};



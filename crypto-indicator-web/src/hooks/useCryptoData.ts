import { useCallback, useEffect, useState } from 'react';

import { CACHE_KEYS,CacheService } from '@/services/CacheService';

import { useApiClient } from './useApiClient';

import type { CryptoCurrencyStatisticsDto } from '@/generated';

interface UseCryptoDataReturn {
  data: CryptoCurrencyStatisticsDto[];
  error: string | null;
  fetchData: () => Promise<void>;
  refetch: () => Promise<void>;
}

export const useCryptoData = (): UseCryptoDataReturn => {
  // Initialize with cached data immediately (no empty state)
  const [data, setData] = useState<CryptoCurrencyStatisticsDto[]>(() => {
    return CacheService.load<CryptoCurrencyStatisticsDto>(CACHE_KEYS.CRYPTO_STATISTICS) ?? [];
  });
  const [error, setError] = useState<string | null>(null);
  const client = useApiClient();

  const fetchData = useCallback(async () => {
    setError(null);

    try {
      const result = await client.CryptoStatisticsController_getCryptoStatistics();
      setData(result);
      // Save fresh data to cache for next time
      CacheService.save(CACHE_KEYS.CRYPTO_STATISTICS, result);
    } catch (error_) {
      const errorMessage = error_ instanceof Error ? error_.message : 'Failed to fetch cryptocurrency data';
      setError(errorMessage);
      // eslint-disable-next-line no-console
      console.error('Error fetching crypto data:', error_);
    }
  }, [client]);

  // Fetch fresh data on mount (background fetch)
  useEffect(() => {
    void fetchData();
  }, [fetchData]);

  return {
    data,
    error,
    fetchData,
    refetch: fetchData,
  };
};

import { useCallback, useEffect, useState } from "react";

import { defaultApiClient } from "@/generated";
import { CACHE_KEYS,CacheService } from "@/services/CacheService";
import { StockDataService } from "@/services/StockDataService";

import type { StockStatisticsDto } from "@/generated";

interface UseStockDataReturn {
  data: StockStatisticsDto[];
  error: string | null;
  fetchData: () => Promise<void>;
}

const stockDataService = new StockDataService(defaultApiClient);

export const useStockData = (): UseStockDataReturn => {
  // Initialize with cached data immediately (no empty state)
  const [data, setData] = useState<StockStatisticsDto[]>(() => {
    return CacheService.load<StockStatisticsDto>(CACHE_KEYS.STOCK_STATISTICS) ?? [];
  });
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async (): Promise<void> => {
    setError(null);

    try {
      const statistics = await stockDataService.getStatistics();
      setData(statistics);
      // Save fresh data to cache for next time
      CacheService.save(CACHE_KEYS.STOCK_STATISTICS, statistics);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setError(errorMessage);
      // eslint-disable-next-line no-console
      console.error("Failed to fetch stock data:", error);
    }
  }, []);

  // Fetch fresh data on mount (background fetch)
  useEffect(() => {
    void fetchData();
  }, [fetchData]);

  return {
    data,
    error,
    fetchData,
  };
};

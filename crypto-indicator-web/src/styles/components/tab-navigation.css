/**
 * Tab Navigation Component Styles
 *
 * Styles for the TabNavigation component including tab buttons,
 * active states, and responsive design.
 *
 * Dependencies: variables.css, base.css
 */

.tab-navigation {
  margin-bottom: 24px;
}

.tab-list {
  display: flex;
  gap: 4px;
  padding: 4px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
}

.tab-button {
  position: relative;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  background: transparent;
  border: none;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--text-primary);
  background: var(--bg-elevated);
  transform: translateY(-1px);
}

.tab-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-teal);
}

.tab-button.active {
  color: var(--bg-primary);
  background: var(--accent-teal);
  box-shadow: 0 2px 8px rgb(148 226 213 / 0.3);
}

.tab-button.active:hover {
  color: var(--bg-primary);
  background: var(--accent-teal);
  box-shadow: 0 4px 12px rgb(148 226 213 / 0.4);
  transform: translateY(-1px);
}

.tab-icon {
  font-size: 16px;
  line-height: 1;
}

.tab-label {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Mobile responsive */
@media (width <= 480px) {
  .tab-navigation {
    margin-bottom: 8px;
  }

  .tab-button {
    min-width: 80px;
    padding: 10px 16px;
    font-size: 13px;
  }

  .tab-icon {
    font-size: 14px;
  }
}

/* Tablet responsive */
@media (width <= 768px) {
  .tab-navigation {
    margin-bottom: 12px;
  }

  .tab-button {
    min-width: 90px;
    padding: 11px 18px;
  }
}

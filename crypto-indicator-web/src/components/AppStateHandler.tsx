import React from "react";

import { CSS_CLASSES } from "@/constants/app";

import { DashboardHeader } from "./ui/DashboardHeader";
import { ErrorState } from "./ui/ErrorState";

interface AppStateHandlerProps {
  error: string | null;
  onRetry: () => void;
  children: React.ReactNode;
}

export const AppStateHandler: React.FC<AppStateHandlerProps> = ({
  error,
  onRetry,
  children,
}) => {
  if (error !== null && error !== undefined) {
    return (
      <div className={CSS_CLASSES.APP_CONTAINER}>
        <DashboardHeader />
        <ErrorState message={error} onRetry={onRetry} />
      </div>
    );
  }

  return <>{children}</>;
};

import React, { createContext, ReactNode } from 'react';

import { useDarkReader } from '@/hooks/useDarkReader';

interface DarkReaderConfig {
  brightness: number;
  contrast: number;
  sepia: number;
  grayscale?: number;
}

interface DarkReaderContextType {
  isEnabled: boolean;
  isReady: boolean;
  config: DarkReaderConfig;
  enable: (config?: DarkReaderConfig) => void;
  disable: () => void;
  toggle: () => void;
  updateConfig: (config: DarkReaderConfig) => void;
  exportCSS: () => Promise<string>;
  isDarkReaderSupported: boolean;
}

const DarkReaderContext = createContext<DarkReaderContextType | undefined>(
  undefined,
);

interface DarkReaderProviderProps {
  children: ReactNode;
  enabled?: boolean;
  config?: DarkReaderConfig;
  autoDetectSystemTheme?: boolean;
}

export const DarkReaderProvider: React.FC<DarkReaderProviderProps> = ({
  children,
  enabled = true,
  config = {
    brightness: 150,
    contrast: 130,
    sepia: 0,
    grayscale: 0,
  },
  autoDetectSystemTheme = false,
}) => {
  const darkReaderHook = useDarkReader({
    enabled,
    config,
    autoDetectSystemTheme,
  });

  // Show blank white loading screen until DarkReader is ready
  if (enabled && !darkReaderHook.isReady) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: '#fff',
          zIndex: 9999,
        }}
      />
    );
  }

  return (
    <DarkReaderContext.Provider value={darkReaderHook}>
      {children}
    </DarkReaderContext.Provider>
  );
};

interface CachedData<T> {
  data: T[];
  timestamp: number;
  version: string;
}

/**
 * Service for caching data in localStorage with expiration and versioning
 * Implements stale-while-revalidate pattern for instant app startup
 */
export class CacheService {
  private static readonly MINUTES_PER_HOUR = 60;
  private static readonly SECONDS_PER_MINUTE = 60;
  private static readonly MS_PER_SECOND = 1000;
  private static readonly CACHE_DURATION = CacheService.MINUTES_PER_HOUR * CacheService.SECONDS_PER_MINUTE * CacheService.MS_PER_SECOND; // 1 hour
  private static readonly CACHE_VERSION = '1.0.0'; // Increment when data structure changes

  /**
   * Save data to localStorage with timestamp and version
   */
  static save<T>(key: string, data: T[]): void {
    try {
      const cachedData: CachedData<T> = {
        data,
        timestamp: Date.now(),
        version: this.CACHE_VERSION,
      };
      localStorage.setItem(key, JSON.stringify(cachedData));
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to save to cache:', error);
    }
  }

  /**
   * Load data from localStorage
   * Returns cached data even if expired (stale-while-revalidate)
   * Returns null if no cache exists or cache is corrupted
   */
  static load<T>(key: string): T[] | null {
    try {
      const cached = localStorage.getItem(key);
      if (cached === null || cached === '') {
        return null;
      }

      const cachedData = JSON.parse(cached) as CachedData<T>;
      
      // Check version compatibility
      if (cachedData.version !== this.CACHE_VERSION) {
        this.clear(key);
        return null;
      }

      return cachedData.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to load from cache:', error);
      this.clear(key);
      return null;
    }
  }

  /**
   * Check if cached data is expired (for background refresh decisions)
   */
  static isExpired(key: string): boolean {
    try {
      const cached = localStorage.getItem(key);
      if (cached === null || cached === '') {
        return true;
      }

      const cachedData = JSON.parse(cached) as CachedData<unknown>;
      return Date.now() - cachedData.timestamp > this.CACHE_DURATION;
    } catch {
      return true;
    }
  }

  /**
   * Clear specific cache entry
   */
  static clear(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to clear cache:', error);
    }
  }

  /**
   * Clear all cache entries
   */
  static clearAll(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.includes('crypto-') || key.includes('stock-')) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to clear all cache:', error);
    }
  }
}

// Cache keys
export const CACHE_KEYS = {
  CRYPTO_STATISTICS: 'crypto-statistics-cache',
  STOCK_STATISTICS: 'stock-statistics-cache',
} as const;

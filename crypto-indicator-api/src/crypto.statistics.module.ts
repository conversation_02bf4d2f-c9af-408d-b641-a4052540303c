import { join } from 'node:path';

import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ServeStaticModule } from '@nestjs/serve-static';

import FinancialIndicatorRestClient from './api/financial.indicator.api';
import { cacheConfig } from './config/cache.config';
import { CryptoStatisticsController } from './crypto.statistics.controller';
import { CryptoStatisticsService } from './crypto.statistics.service';
import { CachedFinancialIndicatorService } from './services/cached-financial-indicator.service';
import { DataTransformationService } from './services/data-transformation.service';
import { StockDataTransformationService } from './services/stock-data-transformation.service';
import { StockStatisticsController } from './stock.statistics.controller';
import { StockStatisticsService } from './stock.statistics.service';

@Module({
  imports: [
    CacheModule.register(cacheConfig),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', 'crypto-indicator-web', 'build'),
    }),
  ],
  controllers: [CryptoStatisticsController, StockStatisticsController],
  providers: [
    CryptoStatisticsService,
    StockStatisticsService,
    DataTransformationService,
    StockDataTransformationService,
    FinancialIndicatorRestClient,
    {
      provide: 'daemonClient',
      useClass: CachedFinancialIndicatorService,
    },
  ],
})
export class CryptoStatisticsModule {}

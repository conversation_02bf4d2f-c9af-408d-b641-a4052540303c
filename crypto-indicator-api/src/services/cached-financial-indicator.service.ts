import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache } from 'cache-manager';

import FinancialIndicatorRestClient, {
  DaemonClient,
  DaemonResponse,
  IndicatorValue,
  StockDaemonResponse,
} from '../api/financial.indicator.api';
import { CACHE_KEYS, generateIndicatorCacheKey, getCacheConfig } from '../config/cache.config';

@Injectable()
export class CachedFinancialIndicatorService implements DaemonClient {
  private readonly logger = new Logger(CachedFinancialIndicatorService.name);
  private readonly isCacheEnabled: boolean;

  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly originalClient: FinancialIndicatorRestClient,
  ) {
    const cacheConfig = getCacheConfig();
    this.isCacheEnabled = (cacheConfig.ttl ?? 0) > 0;

    if (this.isCacheEnabled) {
      this.logger.log(`Cache enabled with TTL: ${cacheConfig.ttl}ms, Max items: ${cacheConfig.max}`);
    }
    else {
      this.logger.log('Cache disabled via configuration');
    }
  }

  async getCryptoStatistics(): Promise<DaemonResponse> {
    // If caching is disabled, go directly to daemon
    if (!this.isCacheEnabled) {
      return this.originalClient.getCryptoStatistics();
    }

    const cacheKey = CACHE_KEYS.CRYPTO_STATISTICS;

    try {
      const cached = await this.cacheManager.get<DaemonResponse>(cacheKey);
      if (cached) {
        this.logger.log(`Cache HIT for ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache MISS for ${cacheKey}, fetching from daemon`);

      const result = await this.originalClient.getCryptoStatistics();

      await this.cacheManager.set(cacheKey, result);

      this.logger.log(`Cached result for ${cacheKey}`);
      return result;
    }
    catch (cacheError) {
      const errorMessage
        = cacheError instanceof Error ? cacheError.message : String(cacheError);
      this.logger.warn(
        `Cache error for ${cacheKey}: ${errorMessage}, falling back to daemon`,
      );
      return this.originalClient.getCryptoStatistics();
    }
  }

  async getCryptoIndicators(
    symbol: string,
    conversionCurrency: string,
  ): Promise<IndicatorValue[]> {
    // If caching is disabled, go directly to daemon
    if (!this.isCacheEnabled) {
      return this.originalClient.getCryptoIndicators(symbol, conversionCurrency);
    }

    const cacheKey = generateIndicatorCacheKey(
      'crypto',
      symbol,
      conversionCurrency,
    );

    try {
      const cached = await this.cacheManager.get<IndicatorValue[]>(cacheKey);
      if (cached) {
        this.logger.log(`Cache HIT for ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache MISS for ${cacheKey}, fetching from daemon`);

      const result = await this.originalClient.getCryptoIndicators(
        symbol,
        conversionCurrency,
      );

      await this.cacheManager.set(cacheKey, result);

      this.logger.log(`Cached result for ${cacheKey}`);
      return result;
    }
    catch (cacheError) {
      const errorMessage
        = cacheError instanceof Error ? cacheError.message : String(cacheError);
      this.logger.warn(
        `Cache error for ${cacheKey}: ${errorMessage}, falling back to daemon`,
      );
      return this.originalClient.getCryptoIndicators(
        symbol,
        conversionCurrency,
      );
    }
  }

  async getStockStatistics(): Promise<StockDaemonResponse> {
    // If caching is disabled, go directly to daemon
    if (!this.isCacheEnabled) {
      return this.originalClient.getStockStatistics();
    }

    const cacheKey = CACHE_KEYS.STOCK_STATISTICS;

    try {
      const cached = await this.cacheManager.get<StockDaemonResponse>(cacheKey);
      if (cached) {
        this.logger.log(`Cache HIT for ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache MISS for ${cacheKey}, fetching from daemon`);

      const result = await this.originalClient.getStockStatistics();

      await this.cacheManager.set(cacheKey, result);

      this.logger.log(`Cached result for ${cacheKey}`);
      return result;
    }
    catch (cacheError) {
      const errorMessage
        = cacheError instanceof Error ? cacheError.message : String(cacheError);
      this.logger.warn(
        `Cache error for ${cacheKey}: ${errorMessage}, falling back to daemon`,
      );
      return this.originalClient.getStockStatistics();
    }
  }

  async getStockIndicators(
    symbol: string,
    conversionCurrency: string,
  ): Promise<IndicatorValue[]> {
    // If caching is disabled, go directly to daemon
    if (!this.isCacheEnabled) {
      return this.originalClient.getStockIndicators(symbol, conversionCurrency);
    }

    const cacheKey = generateIndicatorCacheKey(
      'stock',
      symbol,
      conversionCurrency,
    );

    try {
      const cached = await this.cacheManager.get<IndicatorValue[]>(cacheKey);
      if (cached) {
        this.logger.log(`Cache HIT for ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache MISS for ${cacheKey}, fetching from daemon`);

      const result = await this.originalClient.getStockIndicators(
        symbol,
        conversionCurrency,
      );

      await this.cacheManager.set(cacheKey, result);

      this.logger.log(`Cached result for ${cacheKey}`);
      return result;
    }
    catch (cacheError) {
      const errorMessage
        = cacheError instanceof Error ? cacheError.message : String(cacheError);
      this.logger.warn(
        `Cache error for ${cacheKey}: ${errorMessage}, falling back to daemon`,
      );
      return this.originalClient.getStockIndicators(symbol, conversionCurrency);
    }
  }
}

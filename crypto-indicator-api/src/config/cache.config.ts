import type { CacheModuleOptions } from '@nestjs/cache-manager';

/**
 * Get cache configuration from environment variables
 * Defaults:
 * - CACHE_ENABLED: true
 * - CACHE_TTL_MINUTES: 30
 * - CACHE_MAX_ITEMS: 1000
 * - CACHE_STORE: memory
 */
export function getCacheConfig(): CacheModuleOptions {
  const enabled = process.env.CACHE_ENABLED?.toLowerCase() !== 'false'; // Default to true
  const ttlMinutes = parseInt(process.env.CACHE_TTL_MINUTES || '30', 10);
  const maxItems = parseInt(process.env.CACHE_MAX_ITEMS || '1000', 10);
  // const store = process.env.CACHE_STORE || 'memory'; // Reserved for future Redis support

  // If caching is disabled, return minimal config
  if (!enabled) {
    return {
      ttl: 0, // No caching
      max: 0,
      isGlobal: true,
    };
  }

  return {
    ttl: ttlMinutes * 60 * 1000, // Convert minutes to milliseconds
    max: maxItems,
    isGlobal: true,
    // Note: For Redis or other stores, additional configuration would be needed
    // Currently only supports in-memory store
  };
}

/**
 * Cache configuration for daemon response caching
 * Configurable via environment variables
 */
export const cacheConfig: CacheModuleOptions = getCacheConfig();

/**
 * Cache key prefixes for different data types
 */
export const CACHE_KEYS = {
  CRYPTO_STATISTICS: 'crypto:statistics',
  STOCK_STATISTICS: 'stock:statistics',
  CRYPTO_INDICATORS: 'crypto:indicators',
  STOCK_INDICATORS: 'stock:indicators',
} as const;

/**
 * Generate cache key for indicator requests
 */
export function generateIndicatorCacheKey(
  type: 'crypto' | 'stock',
  symbol: string,
  conversionCurrency: string,
): string {
  const prefix
    = type === 'crypto'
      ? CACHE_KEYS.CRYPTO_INDICATORS
      : CACHE_KEYS.STOCK_INDICATORS;
  return `${prefix}:${symbol}:${conversionCurrency}`;
}
